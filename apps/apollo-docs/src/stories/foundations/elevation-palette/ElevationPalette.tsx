import React, { use<PERSON>em<PERSON>, useState } from "react"
import { Input, Theme } from "@apollo/ui"
import { CheckCircle } from "@design-systems/apollo-icons"

import styles from "./ElevationPalette.module.css"

// Common utility functions
const handleCopy = async (text: string, setCopied: (value: boolean) => void) => {
  try {
    await navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  } catch (err) {
    console.error("Failed to copy text: ", err)
  }
}

const handleKeyDown = (event: React.KeyboardEvent, cssVariable: string, setCopied: (value: boolean) => void) => {
  if (event.key === "Enter" || event.key === " ") {
    event.preventDefault()
    handleCopy(cssVariable, setCopied)
  }
}

interface ElevationToken {
  cssVariable: string
  displayName: string
  category: string
  subcategory?: string
  value?: string
  elevationLevel?: string
  properties?: {
    xAxis: string
    yAxis: string
    blur: string
    spread: string
    color: string
  }
}

interface ElevationSwatchProps {
  token: ElevationToken
}

const ElevationSwatch: React.FC<ElevationSwatchProps> = ({ token }) => {
  const [copied, setCopied] = useState(false)

  // Check if this is an alias token that should show a visual preview
  const isAliasWithPreview = token.category === "Alias" && token.elevationLevel && token.properties

  // Create box-shadow style for alias tokens with preview
  const boxShadowStyle = isAliasWithPreview && token.properties
    ? {
        boxShadow: `${token.properties.xAxis} ${token.properties.yAxis} ${token.properties.blur} ${token.properties.spread} ${token.properties.color}`,
      }
    : {}

  // Check if this is an unused elevation (all values are 0 or transparent)
  const isUnused = token.properties &&
    token.properties.blur === "0px" &&
    token.properties.spread === "0px" &&
    (token.properties.color === "transparent" || token.properties.color === "0px")

  return (
    <button
      className={styles.elevationSwatch}
      onClick={() => handleCopy(token.cssVariable, setCopied)}
      onKeyDown={(e) => handleKeyDown(e, token.cssVariable, setCopied)}
      aria-label={`Copy ${token.cssVariable} to clipboard`}
      title={`Click to copy ${token.cssVariable}`}
    >
      {copied && (
        <div
          style={{
            position: "absolute",
            top: "4px",
            right: "4px",
            display: "flex",
            alignItems: "center",
            gap: "4px",
            padding: "4px 8px",
            backgroundColor: "#10b981",
            color: "white",
            borderRadius: "4px",
            fontSize: "11px",
            fontWeight: "500",
            zIndex: 1,
          }}
        >
          <CheckCircle size={12} />
          Copied
        </div>
      )}

      {/* Show visual preview for alias tokens with elevation level */}
      {isAliasWithPreview && (
        <div className={styles.elevationPreview}>
          <div className={styles.elevationContainer}>
            <div
              className={styles.elevationDemo}
              style={{
                ...boxShadowStyle,
                opacity: isUnused ? 0.5 : 1,
              }}
            >
              {isUnused ? "Unused" : "Card"}
            </div>
          </div>
          {token.properties && (
            <div className={styles.elevationProperties}>
              <div className={styles.elevationProperty}>
                <span className={styles.elevationPropertyLabel}>X-axis:</span>
                <span className={styles.elevationPropertyValue}>{token.properties.xAxis}</span>
              </div>
              <div className={styles.elevationProperty}>
                <span className={styles.elevationPropertyLabel}>Y-axis:</span>
                <span className={styles.elevationPropertyValue}>{token.properties.yAxis}</span>
              </div>
              <div className={styles.elevationProperty}>
                <span className={styles.elevationPropertyLabel}>Blur:</span>
                <span className={styles.elevationPropertyValue}>{token.properties.blur}</span>
              </div>
              <div className={styles.elevationProperty}>
                <span className={styles.elevationPropertyLabel}>Spread:</span>
                <span className={styles.elevationPropertyValue}>{token.properties.spread}</span>
              </div>
              <div className={styles.elevationProperty}>
                <span className={styles.elevationPropertyLabel}>Color:</span>
                <span className={styles.elevationPropertyValue}>{token.properties.color}</span>
              </div>
            </div>
          )}
        </div>
      )}

      <div className={styles.elevationSwatchInfo}>
        <div className={styles.elevationSwatchVariable}>{token.cssVariable}</div>
        <div className={styles.elevationSwatchName}>{token.displayName}</div>
        {token.value && (
          <div className={styles.elevationSwatchValue}>{token.value}</div>
        )}
      </div>
    </button>
  )
}

interface ElevationGroupProps {
  title: string
  tokens: ElevationToken[]
  searchTerm: string
}

const ElevationGroup: React.FC<ElevationGroupProps> = ({
  title,
  tokens,
  searchTerm,
}) => {
  const filteredTokens = tokens.filter(
    (token) =>
      token.cssVariable.toLowerCase().includes(searchTerm.toLowerCase()) ||
      token.displayName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const groupedTokens = useMemo(() => {
    return filteredTokens.reduce((groups: Record<string, ElevationToken[]>, token) => {
      const key = token.subcategory || "default"
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(token)
      return groups
    }, {})
  }, [filteredTokens])

  if (filteredTokens.length === 0) return null

  return (
    <div className={styles.elevationGroup}>
      <h2 id={title} className={styles.elevationGroupTitle}>
        {title}
      </h2>
      {Object.entries(groupedTokens).map(([subcategory, subTokens]) => (
        <div key={subcategory} className={styles.elevationSubgroup}>
          {subcategory !== "default" && (
            <h4 className={styles.elevationSubgroupTitle}>{subcategory}</h4>
          )}
          <div className={styles.elevationGrid}>
            {subTokens.map((token) => (
              <ElevationSwatch key={token.cssVariable} token={token} />
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

// Extract elevation tokens from CSS variables
const getElevationTokens = (): {
  aliasTokens: ElevationToken[]
  baseTokens: ElevationToken[]
} => {
  const aliasTokens: ElevationToken[] = []
  const baseTokens: ElevationToken[] = []

  // Alias elevation tokens - individual property tokens
  const aliasElevationLevels = [
    {
      level: "elevations1",
      display: "Elevation 1 (Active)",
      properties: {
        xAxis: "0px", // var(--apl-base-elevation-x-axis-none)
        yAxis: "2px", // var(--apl-base-elevation-y-axis-2)
        blur: "4px", // var(--apl-base-elevation-blur-4)
        spread: "0px", // var(--apl-base-spacing-space1)
        color: "rgba(0, 0, 0, 0.1)", // var(--apl-base-color-overlay-black-10)
      },
    },
    {
      level: "elevations2",
      display: "Elevation 2 (Unused)",
      properties: {
        xAxis: "0px",
        yAxis: "0px",
        blur: "0px",
        spread: "0px",
        color: "transparent",
      },
    },
    {
      level: "elevations3",
      display: "Elevation 3 (Unused)",
      properties: {
        xAxis: "0px",
        yAxis: "0px",
        blur: "0px",
        spread: "0px",
        color: "transparent",
      },
    },
  ]

  // Create individual tokens for each property of each elevation level
  aliasElevationLevels.forEach((level) => {
    const propertyNames = [
      { key: "xAxis", name: "x-axis", display: "X-axis" },
      { key: "yAxis", name: "y-axis", display: "Y-axis" },
      { key: "blur", name: "blur", display: "Blur" },
      { key: "spread", name: "spread", display: "Spread" },
      { key: "color", name: "color", display: "Color" },
    ]

    propertyNames.forEach((prop) => {
      aliasTokens.push({
        cssVariable: `--apl-alias-elevation-${level.level}-${prop.name}`,
        displayName: `${level.display} - ${prop.display}`,
        category: "Alias",
        subcategory: level.display,
        value: level.properties[prop.key as keyof typeof level.properties],
        elevationLevel: level.level,
        properties: level.properties,
      })
    })
  })

  // Base elevation tokens - individual properties
  const baseElevationData = [
    // Blur tokens
    { name: "blur-none", value: "0px", display: "Blur None", subcategory: "Blur" },
    { name: "blur-2", value: "2px", display: "Blur 2px", subcategory: "Blur" },
    { name: "blur-4", value: "4px", display: "Blur 4px", subcategory: "Blur" },
    { name: "blur-6", value: "6px", display: "Blur 6px", subcategory: "Blur" },
    { name: "blur-8", value: "8px", display: "Blur 8px", subcategory: "Blur" },
    { name: "blur-10", value: "10px", display: "Blur 10px", subcategory: "Blur" },
    { name: "blur-12", value: "12px", display: "Blur 12px", subcategory: "Blur" },
    { name: "blur-14", value: "14px", display: "Blur 14px", subcategory: "Blur" },
    { name: "blur-16", value: "16px", display: "Blur 16px", subcategory: "Blur" },
    { name: "blur-18", value: "18px", display: "Blur 18px", subcategory: "Blur" },
    { name: "blur-20", value: "20px", display: "Blur 20px", subcategory: "Blur" },
    { name: "blur-22", value: "22px", display: "Blur 22px", subcategory: "Blur" },
    { name: "blur-24", value: "24px", display: "Blur 24px", subcategory: "Blur" },
    { name: "blur-26", value: "26px", display: "Blur 26px", subcategory: "Blur" },
    { name: "blur-28", value: "28px", display: "Blur 28px", subcategory: "Blur" },
    { name: "blur-30", value: "30px", display: "Blur 30px", subcategory: "Blur" },
    { name: "blur-32", value: "32px", display: "Blur 32px", subcategory: "Blur" },

    // Spread tokens
    { name: "spread-none", value: "0px", display: "Spread None", subcategory: "Spread" },
    { name: "spread-1", value: "1px", display: "Spread 1px", subcategory: "Spread" },
    { name: "spread-2", value: "2px", display: "Spread 2px", subcategory: "Spread" },
    { name: "spread-4", value: "4px", display: "Spread 4px", subcategory: "Spread" },
    { name: "spread-6", value: "6px", display: "Spread 6px", subcategory: "Spread" },
    { name: "spread-8", value: "8px", display: "Spread 8px", subcategory: "Spread" },
    { name: "spread-10", value: "10px", display: "Spread 10px", subcategory: "Spread" },
    { name: "spread-12", value: "12px", display: "Spread 12px", subcategory: "Spread" },
    { name: "spread-14", value: "14px", display: "Spread 14px", subcategory: "Spread" },
    { name: "spread-16", value: "16px", display: "Spread 16px", subcategory: "Spread" },
    { name: "spread-18", value: "18px", display: "Spread 18px", subcategory: "Spread" },
    { name: "spread-20", value: "20px", display: "Spread 20px", subcategory: "Spread" },
    { name: "spread-22", value: "22px", display: "Spread 22px", subcategory: "Spread" },
    { name: "spread-24", value: "24px", display: "Spread 24px", subcategory: "Spread" },

    // X-axis tokens
    { name: "x-axis-none", value: "0px", display: "X-axis None", subcategory: "X-axis" },
    { name: "x-axis-1", value: "1px", display: "X-axis 1px", subcategory: "X-axis" },
    { name: "x-axis-2", value: "2px", display: "X-axis 2px", subcategory: "X-axis" },
    { name: "x-axis-4", value: "4px", display: "X-axis 4px", subcategory: "X-axis" },
    { name: "x-axis-6", value: "6px", display: "X-axis 6px", subcategory: "X-axis" },
    { name: "x-axis-8", value: "8px", display: "X-axis 8px", subcategory: "X-axis" },
    { name: "x-axis-10", value: "10px", display: "X-axis 10px", subcategory: "X-axis" },
    { name: "x-axis-12", value: "12px", display: "X-axis 12px", subcategory: "X-axis" },
    { name: "x-axis-14", value: "14px", display: "X-axis 14px", subcategory: "X-axis" },
    { name: "x-axis-16", value: "16px", display: "X-axis 16px", subcategory: "X-axis" },
    { name: "x-axis-18", value: "18px", display: "X-axis 18px", subcategory: "X-axis" },
    { name: "x-axis-20", value: "20px", display: "X-axis 20px", subcategory: "X-axis" },
    { name: "x-axis-22", value: "22px", display: "X-axis 22px", subcategory: "X-axis" },
    { name: "x-axis-24", value: "24px", display: "X-axis 24px", subcategory: "X-axis" },

    // Y-axis tokens
    { name: "y-axis-none", value: "0px", display: "Y-axis None", subcategory: "Y-axis" },
    { name: "y-axis-1", value: "1px", display: "Y-axis 1px", subcategory: "Y-axis" },
    { name: "y-axis-2", value: "2px", display: "Y-axis 2px", subcategory: "Y-axis" },
    { name: "y-axis-4", value: "4px", display: "Y-axis 4px", subcategory: "Y-axis" },
    { name: "y-axis-6", value: "6px", display: "Y-axis 6px", subcategory: "Y-axis" },
    { name: "y-axis-8", value: "8px", display: "Y-axis 8px", subcategory: "Y-axis" },
    { name: "y-axis-10", value: "10px", display: "Y-axis 10px", subcategory: "Y-axis" },
    { name: "y-axis-12", value: "12px", display: "Y-axis 12px", subcategory: "Y-axis" },
    { name: "y-axis-14", value: "14px", display: "Y-axis 14px", subcategory: "Y-axis" },
    { name: "y-axis-16", value: "16px", display: "Y-axis 16px", subcategory: "Y-axis" },
    { name: "y-axis-18", value: "18px", display: "Y-axis 18px", subcategory: "Y-axis" },
    { name: "y-axis-20", value: "20px", display: "Y-axis 20px", subcategory: "Y-axis" },
    { name: "y-axis-22", value: "22px", display: "Y-axis 22px", subcategory: "Y-axis" },
    { name: "y-axis-24", value: "24px", display: "Y-axis 24px", subcategory: "Y-axis" },
  ]

  baseElevationData.forEach((item) => {
    baseTokens.push({
      cssVariable: `--apl-base-elevation-${item.name}`,
      displayName: item.display,
      category: "Base",
      subcategory: item.subcategory,
      value: item.value,
    })
  })

  return { aliasTokens, baseTokens }
}

export const ElevationPalette: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("")
  const { aliasTokens, baseTokens } = useMemo(() => getElevationTokens(), [])

  return (
    <Theme>
      <div className={styles.elevationPalette}>
        <div className={styles.searchContainer}>
          <Input
            placeholder="Search elevation tokens..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            fullWidth
          />
        </div>

        <div className={styles.elevationGroups}>
          <ElevationGroup
            title="Alias Tokens"
            tokens={aliasTokens}
            searchTerm={searchTerm}
          />

          <ElevationGroup
            title="Base Tokens"
            tokens={baseTokens}
            searchTerm={searchTerm}
          />
        </div>
      </div>
    </Theme>
  )
}
