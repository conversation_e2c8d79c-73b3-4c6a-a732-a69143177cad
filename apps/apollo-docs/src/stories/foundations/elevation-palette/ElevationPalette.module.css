.elevationPalette {
  display: flex;
  flex-direction: column;
  gap: 32px;
  max-width: 100%;
}

.searchContainer {
  max-width: 400px;
}

.elevationGroups {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.elevationGroup {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.elevationGroupTitle {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: var(--apl-alias-color-text-text);
  border-bottom: 1px solid var(--apl-alias-color-border-border);
  padding-bottom: 8px;
}

.elevationSubgroup {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.elevationSubgroupTitle {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  color: var(--apl-alias-color-text-text-secondary);
}

.elevationList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.elevationListItem {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: var(--apl-alias-color-background-and-surface-surface);
  border: 1px solid var(--apl-alias-color-border-border);
  border-radius: var(--apl-alias-radius-radius3);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.elevationListItem:hover {
  border-color: var(--apl-alias-color-border-border-hover);
  background: var(--apl-alias-color-background-and-surface-background);
}

.elevationListItem:focus {
  outline: 2px solid var(--apl-alias-color-border-focus);
  outline-offset: 2px;
}

/* Removed elevation preview styles - using list format now */

.elevationTokenInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.elevationTokenHeader {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.elevationTokenVariable {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 14px;
  font-weight: 600;
  color: var(--apl-alias-color-text-text);
  word-break: break-all;
}

.elevationTokenName {
  font-size: 13px;
  color: var(--apl-alias-color-text-text-secondary);
}

.elevationTokenValue {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  color: var(--apl-alias-color-text-text-secondary);
  background: var(--apl-alias-color-background-and-surface-background);
  padding: 4px 8px;
  border-radius: var(--apl-alias-radius-radius1);
  border: 1px solid var(--apl-alias-color-border-border);
}

.elevationUsageExample {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
  padding: 12px;
  background: var(--apl-alias-color-background-and-surface-background);
  border-radius: var(--apl-alias-radius-radius2);
  border: 1px solid var(--apl-alias-color-border-border);
}

.elevationUsageLabel {
  font-size: 12px;
  font-weight: 600;
  color: var(--apl-alias-color-text-text);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.elevationUsageCode {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  color: var(--apl-alias-color-text-text);
  background: var(--apl-alias-color-background-and-surface-surface);
  padding: 8px 12px;
  border-radius: var(--apl-alias-radius-radius1);
  border: 1px solid var(--apl-alias-color-border-border);
  word-break: break-all;
  line-height: 1.4;
}

/* Removed old elevation properties styles - using new list format */
