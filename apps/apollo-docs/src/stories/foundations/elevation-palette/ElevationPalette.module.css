.elevationPalette {
  display: flex;
  flex-direction: column;
  gap: 32px;
  max-width: 100%;
}

.searchContainer {
  max-width: 400px;
}

.elevationGroups {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.elevationGroup {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.elevationGroupTitle {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: var(--apl-alias-color-text-text);
  border-bottom: 1px solid var(--apl-alias-color-border-border);
  padding-bottom: 8px;
}

.elevationSubgroup {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.elevationSubgroupTitle {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  color: var(--apl-alias-color-text-text-secondary);
}

.elevationGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.elevationSwatch {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: var(--apl-alias-color-background-and-surface-surface);
  border: 1px solid var(--apl-alias-color-border-border);
  border-radius: var(--apl-alias-radius-radius2);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.elevationSwatch:hover {
  border-color: var(--apl-alias-color-border-border-hover);
}

.elevationSwatch:focus {
  outline: 2px solid var(--apl-alias-color-border-focus);
  outline-offset: 2px;
}

/* Removed elevation preview styles - using list format now */

.elevationSwatchInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.elevationSwatchVariable {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 11px;
  color: var(--apl-alias-color-text-text-secondary);
  word-break: break-all;
}

.elevationSwatchName {
  font-size: 13px;
  font-weight: 500;
  color: var(--apl-alias-color-text-text);
}

.elevationSwatchValue {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  color: var(--apl-alias-color-text-text);
  font-weight: 600;
}

/* Removed old elevation properties styles - using new list format */
